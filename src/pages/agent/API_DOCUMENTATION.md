# HubbleAgent API 接口文档

## 概述

HubbleAgent 是一个基于大语言模型的智能合同分析系统，提供文件上传、智能对话、文档比对等功能。

**Base URL**: `http://localhost:8080`

## 接口列表

### 1. 初始化聊天会话

**接口描述**: 上传文件并创建新的聊天会话

**请求信息**:
- **URL**: `POST /hubble-agent/init-chat`
- **Content-Type**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 是 | 要上传的文件（支持 PDF、Word 等格式） |
| fileName | String | 是 | 文件名称 |

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/hubble-agent/init-chat \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@contract.pdf' \
  -F 'fileName=合同文件.pdf'
```

**响应格式**:
```json
{
  "data": 3915459655131799557,
  "message": "success",
  "code": 200
}
```

**响应参数**:
| 参数名 | 类型 | 必返回 | 描述 |
|--------|------|--------|------|
| data | Long | 是 | 聊天会话 ID，后续对话需要使用此 ID |
| message | String | 是 | 响应消息，成功时为 "success" |
| code | Integer | 是 | 响应状态码，成功时为 200 |

---

### 2. 上传文件到现有会话

**接口描述**: 向已存在的聊天会话添加新文件

**请求信息**:
- **URL**: `POST /hubble-agent/{chatId}/upload-file`
- **Content-Type**: `multipart/form-data`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| chatId | Long | 是 | 聊天会话 ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 是 | 要上传的文件 |
| fileName | String | 是 | 文件名称 |

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/hubble-agent/3915459655131799557/upload-file \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@contract2.pdf' \
  -F 'fileName=第二份合同.pdf'
```

**响应格式**:
```json
{
  "data": 3915459679332933641,
  "message": "success", 
  "code": 200
}
```

**响应参数**:
| 参数名 | 类型 | 必返回 | 描述 |
|--------|------|--------|------|
| data | Long | 是 | 文件 ID，用于后续引用该文件 |
| message | String | 是 | 响应消息，成功时为 "success" |
| code | Integer | 是 | 响应状态码，成功时为 200 |

---

### 3. 智能对话（SSE 流式接口）

**接口描述**: 与 AI 进行实时对话，支持流式响应

**请求信息**:
- **URL**: `POST /hubble-agent/event-stream/{chatId}/chat`
- **Content-Type**: `application/json`
- **Accept**: `text/event-stream`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| chatId | Long | 是 | 聊天会话 ID |

**请求体格式**:

该接口支持两种不同的请求格式，用于不同的交互场景：

#### 场景1：开始对话（用户发起新的对话）
```json
{
  "userContent": "请帮我比对这两份合同",
  "uploadFile": {
    "fileName": "经销合同B.pdf",
    "fileId": 3915486250089396225
  }
}
```

#### 场景2：用户确认操作（点击按钮确认）
```json
{
  "buttonType": "AUTO_APPROVED"
}
```

**请求参数说明**:

**场景1参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| userContent | String | 是 | 用户输入的对话内容 |
| uploadFile | Object | 否 | 上传的文件信息（如果有新文件） |
| uploadFile.fileId | Long | 是 | 文件 ID |
| uploadFile.fileName | String | 是 | 文件名称 |

**场景2参数**:
| 参数名 | 类型 | 必填 | 可能值 | 描述 |
|--------|------|------|--------|------|
| buttonType | String | 是 | `ENABLED`、`REJECTED`、`AUTO_APPROVED` | 用户操作类型 |

**buttonType 取值说明**:
| 取值 | 描述 | 使用场景 |
|------|------|----------|
| `ENABLED` | 同意执行 | 用户点击"确认"或"同意"按钮 |
| `REJECTED` | 拒绝执行 | 用户点击"取消"或"拒绝"按钮 |
| `AUTO_APPROVED` | 自动执行 | 系统自动同意执行（无需用户确认） |

**响应格式** (SSE 流式数据):

#### 普通文本响应
```
data: {"role":"assistant","content":"我来帮您比对这两份合同。","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"正在分析文档内容...","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":null,"type":"TEXT","status":"FINISHED"}
```

#### 工具确认响应
```
data: {"role":"assistant","content":"call_77d90700db35471ba4699e","type":"FUNCTION_CALL","status":"WAITING_CONFIRMATION"}
```

#### 合同比对场景完整响应示例
```
data: {"role":"assistant","content":"✅ 文档","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"比对任务","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"已创建完成！\n\n📋","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":" 任务详情：\n-","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":" 任务ID: 3","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"915486","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"493519","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"042563","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"\n- 状态:","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":" 已创建，正在处理中","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"\n- 比对","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"文档: 文档经销合同","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"A.pdf vs 文档经销","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"合同B.pdf\n\n🎯","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":" 比对任务已成功","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"提交到系统，您可以点击","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":" [COMPARE_RESULT_LINK:391548649351904","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"2563] 查询","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":"详细的比对结果。","type":"TEXT","status":"TYPING"}

data: {"role":"assistant","content":null,"type":"TEXT","status":"FINISHED"}
```

**SSE 响应参数**:
| 参数名 | 类型 | 必返回 | 可能值 | 描述 |
|--------|------|--------|--------|------|
| role | String | 是 | `assistant` | 角色，固定为 AI 助手 |
| content | String | 是 | 任意文本或null | 消息内容，FINISHED状态时可能为null |
| type | String | 是 | `TEXT`、`FUNCTION_CALL` | 消息类型 |
| status | String | 是 | 见下表 | 当前响应状态 |

**Status 状态详细说明**:
| 状态值 | 何时出现 | Content 内容 | 前端处理建议 |
|--------|----------|-------------|-------------|
| `TYPING` | AI 正在生成回复 | 实时生成的文本片段 | 显示打字动画，实时追加 content 到消息 |
| `FINISHED` | 当前回复完成 | null 或最后的文本片段 | 停止打字动画，完成当前消息显示 |
| `WAITING_CONFIRMATION` | AI 需要执行工具前确认 | 工具调用ID | **显示确认按钮**，询问用户是否执行工具 |
| `ERROR` | 处理过程中发生错误 | 错误描述信息 | 显示错误提示，停止 SSE 连接 |

**注意**: 所有状态值均为枚举类型，前端应严格按照上述值进行判断处理。

## 特殊处理说明

### 合同比对结果链接处理

在合同比对场景中，AI 会返回包含特殊占位符 `[COMPARE_RESULT_LINK:任务ID]` 的文本内容，前端需要识别并替换为实际的可点击链接。

**占位符格式**:
- 格式：`[COMPARE_RESULT_LINK:任务ID]`
- 示例：`[COMPARE_RESULT_LINK:3915486493519042563]`
- 说明：方括号内包含固定前缀 `COMPARE_RESULT_LINK:` 和实际的任务 ID

**前端处理要求**:
1. **识别占位符**：检测 AI 返回内容中是否包含 `[COMPARE_RESULT_LINK:数字]` 格式的文本
2. **提取任务 ID**：从占位符中解析出任务 ID（冒号后面的数字部分）
3. **替换为链接**：将整个占位符替换为可点击的链接元素
4. **链接跳转**：点击链接时调用获取比对结果接口或跳转到结果页面

**处理示例**:
- **原始文本**：`您可以点击 [COMPARE_RESULT_LINK:3915486493519042563] 查询详细的比对结果`
- **替换后**：`您可以点击 <链接>查看详细比对结果</链接> 查询详细的比对结果`
- **链接行为**：点击时调用 `GET /hubble-agent/compare-result/3915486493519042563`

**Type 类型详细说明**:
| 类型值 | 何时使用 | Content 格式 | 说明 |
|--------|----------|-------------|------|
| `TEXT` | 普通文本回复 | 字符串文本 | 用户可直接阅读的内容 |
| `FUNCTION_CALL` | 工具调用确认 | 调用ID字符串 | 如：`call_77d90700db35471ba4699e` |

---

### 4. 获取文档比对结果

**接口描述**: 根据比对任务 ID 获取详细的比对结果

**请求信息**:
- **URL**: `GET /hubble-agent/compare-result/{eventId}`
- **Content-Type**: `application/json`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| eventId | Long | 是 | 比对任务 ID（从比对工具返回结果中获取） |

**请求示例**:
```bash
curl -X GET \
  http://localhost:8080/hubble-agent/compare-result/3915459932191381507 \
  -H 'Content-Type: application/json'
```

**响应格式**:
```json
{
  "data": {
    "eventId": 3915459932191381507,
    "status": "COMPLETED",
    "documentCompareDetailTexts": [
      {
        "pageNumber": 1,
        "originalText": "甲方：北京科技有限公司",
        "comparedText": "甲方：上海科技有限公司", 
        "changeType": "MODIFIED",
        "position": {
          "x": 100,
          "y": 200,
          "width": 200,
          "height": 20
        }
      }
    ],
    "summary": {
      "totalChanges": 15,
      "addedCount": 3,
      "deletedCount": 2,
      "modifiedCount": 10
    }
  },
  "message": "success",
  "code": 200
}
```

**响应参数**:
| 参数名 | 类型 | 必返回 | 可能值 | 描述 |
|--------|------|--------|--------|------|
| data | Object | 是 | - | 比对结果数据对象 |
| message | String | 是 | `success` | 响应消息 |
| code | Integer | 是 | `200` | 响应状态码 |

**data 对象字段说明**:
| 参数名 | 类型 | 必返回 | 可能值 | 描述 |
|--------|------|--------|--------|------|
| eventId | Long | 是 | 任意数字 | 比对任务 ID |
| status | String | 是 | `PROCESSING`、`COMPLETED`、`FAILED` | 比对任务状态 |
| documentCompareDetailTexts | Array | 否 | - | 详细比对结果列表，status为COMPLETED时返回 |
| summary | Object | 否 | - | 比对结果摘要，status为COMPLETED时返回 |

**documentCompareDetailTexts 数组元素字段**:
| 参数名 | 类型 | 必返回 | 可能值 | 描述 |
|--------|------|--------|--------|------|
| pageNumber | Integer | 是 | 1,2,3... | 页码，从1开始 |
| originalText | String | 是 | 任意文本 | 原始文档中的文本内容 |
| comparedText | String | 是 | 任意文本 | 比对文档中的文本内容 |
| changeType | String | 是 | `ADDED`、`DELETED`、`MODIFIED` | 变更类型 |

**changeType 变更类型说明**:
| 取值 | 描述 | originalText | comparedText |
|------|------|-------------|-------------|
| `ADDED` | 新增内容 | 空字符串或null | 新增的文本内容 |
| `DELETED` | 删除内容 | 被删除的文本内容 | 空字符串或null |
| `MODIFIED` | 修改内容 | 原始文本内容 | 修改后的文本内容 |
| position | Object | 否 | - | 文本在页面中的位置坐标信息 |

**position 对象字段**:
| 参数名 | 类型 | 必返回 | 描述 |
|--------|------|--------|------|
| x | Integer | 是 | 文本左上角 X 坐标 |
| y | Integer | 是 | 文本左上角 Y 坐标 |
| width | Integer | 是 | 文本区域宽度 |
| height | Integer | 是 | 文本区域高度 |

**summary 对象字段**:
| 参数名 | 类型 | 必返回 | 描述 |
|--------|------|--------|------|
| totalChanges | Integer | 是 | 总变更数量 |
| addedCount | Integer | 是 | 新增内容数量 |
| deletedCount | Integer | 是 | 删除内容数量 |
| modifiedCount | Integer | 是 | 修改内容数量 |

---

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 404 | 资源不存在 | 检查聊天 ID 或文件 ID 是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 使用流程

### 典型的文档比对流程：

1. **上传第一份文档**
   ```bash
   POST /hubble-agent/init-chat
   # 返回 chatId: 123456
   ```

2. **上传第二份文档**
   ```bash
   POST /hubble-agent/123456/upload-file
   # 返回 fileId: 789012
   ```

3. **发起比对请求**
   ```javascript
   // 用户发起对话请求
   POST /hubble-agent/event-stream/123456/chat
   {
     "userContent": "请帮我比对这两份合同",
     "uploadFile": {
       "fileId": 789012,
       "fileName": "经销合同B.pdf"
     }
   }
   // AI 可能会询问是否执行比对工具
   ```

4. **用户确认执行工具**（如果 AI 需要确认）
   ```json
   // 用户点击确认按钮
   POST /hubble-agent/event-stream/123456/chat
   {
     "buttonType": "ENABLED"
   }
   // 或者用户点击拒绝按钮
   {
     "buttonType": "REJECTED"
   }
   // AI 根据用户选择执行或跳过工具，执行时返回任务 ID: 345678
   ```

5. **获取详细比对结果**
   ```bash
   GET /hubble-agent/compare-result/345678
   ```

## 交互模式说明

### 模式1：直接对话模式
用户直接发送消息，AI 立即处理并返回结果：
```json
{
  "userContent": "请分析这份合同的风险点"
}
```

### 模式2：工具确认模式
当 AI 需要执行某些工具（如文档比对）时，会先询问用户确认：

1. **AI 询问阶段**：AI 返回 `status: "WAITING_CONFIRMATION"` 和 `type: "FUNCTION_CALL"`
2. **用户确认阶段**：用户通过 `buttonType` 参数确认是否执行
   - `"ENABLED"`: 同意执行工具
   - `"REJECTED"`: 拒绝执行工具
   - `"AUTO_APPROVED"`: 自动执行（通常用于系统自动化场景）

### 完整的状态流转
```
用户发起对话 → AI分析需求 →
├─ 直接回复 → status: TYPING → status: FINISHED
└─ 需要工具 → status: WAITING_CONFIRMATION →
   ├─ 用户确认(ENABLED) → 执行工具 → status: TYPING → status: FINISHED
   └─ 用户拒绝(REJECTED) → 跳过工具 → status: TYPING → status: FINISHED
```
